'use client';

import { Slider } from '@heroui/react';
import { useState } from 'react';

export default function PriceRangeSlider() {
  const [range, setRange] = useState<[number, number]>([0, 10000]);

  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    if (!isNaN(value)) {
      const clamped = Math.min(value, range[1]);
      setRange([Math.max(0, clamped), range[1]]);
    }
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value);
    if (!isNaN(value)) {
      const clamped = Math.max(value, range[0]);
      setRange([range[0], Math.min(10000, clamped)]);
    }
  };

  return (
    <div className="rounded-2xl bg-white p-6 w-full max-w-sm shadow-md space-y-4">
      {/* Min / Max Inputs */}
      <div className="flex gap-4">
        <input
          type="number"
          value={range[0]}
          onChange={handleMinChange}
          placeholder="Min"
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-200"
        />
        <input
          type="number"
          value={range[1]}
          onChange={handleMaxChange}
          placeholder="Max"
          className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-200"
        />
      </div>

      {/* Controlled Range Slider */}
      <Slider
        className="max-w-md"
        value={range}
        onChange={(val) => setRange(val as [number, number])}
        formatOptions={{ style: 'currency', currency: 'USD' }}
        label="Price Range"
        maxValue={10000}
        minValue={0}
        step={50}
        size="sm"
      />
    </div>
  );
}
