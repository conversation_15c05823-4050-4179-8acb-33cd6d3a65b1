.calendar-wrapper {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  max-width: fit-content;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
  font-family: sans-serif;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.calendar-tabs {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.tab.selected {
  color: #7C3AED;
  font-weight: 500;
}

.calendar-range-label {
  font-weight: 600;
  color: #333;
}

.calendar-confirm {
  color: #7C3AED;
  font-size: 1.25rem;
}

/* DateRange custom overrides */
.rdrCalendarWrapper {
  border: none;
}

.rdrDateRangeWrapper {
  width: auto;
}

.rdrMonth {
  margin: 0 1rem;
}

.rdrMonthName {
  font-size: 1rem;
  font-weight: 600;
  color: #111;
}

.rdrDayToday .rdrDayNumber span {
  border-bottom: none !important;
}

.rdrDayNumber span {
  font-size: 0.8rem;
  color: #444;
}

.rdrDay.rdrDayHovered,
.rdrDay:focus,
.rdrDay.rdrStartEdge,
.rdrDay.rdrEndEdge,
.rdrDay.rdrInRange {
  border-radius: 6px;
}

.rdrDay.rdrStartEdge,
.rdrDay.rdrEndEdge {
  background: #7C3AED !important;
  color: white;
}

.rdrDay.rdrInRange {
  background: #EDE9FE;
  color: #7C3AED;
}

.rdrDayDisabled {
  opacity: 0.4;
  cursor: not-allowed;
}
